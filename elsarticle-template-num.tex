%% 
%% Copyright 2007-2025 Elsevier Ltd
%% 
%% This file is part of the 'Elsarticle Bundle'.
%% ---------------------------------------------
%% 
%% It may be distributed under the conditions of the LaTeX Project Public
%% License, either version 1.3 of this license or (at your option) any
%% later version.  The latest version of this license is in
%%    http://www.latex-project.org/lppl.txt
%% and version 1.3 or later is part of all distributions of LaTeX
%% version 1999/12/01 or later.
%% 
%% The list of all files belonging to the 'Elsarticle Bundle' is
%% given in the file `manifest.txt'.
%% 
%% Template article for Elsevier's document class `elsarticle'
%% with numbered style bibliographic references
%% SP 2008/03/01
%% $Id: elsarticle-template-num.tex 272 2025-01-09 17:36:26Z rishi $
%%
\documentclass[preprint,12pt]{elsarticle}

%% Use the option review to obtain double line spacing
%% \documentclass[authoryear,preprint,review,12pt]{elsarticle}

%% Use the options 1p,twocolumn; 3p; 3p,twocolumn; 5p; or 5p,twocolumn
%% for a journal layout:
%% \documentclass[final,1p,times]{elsarticle}
%% \documentclass[final,1p,times,twocolumn]{elsarticle}
%% \documentclass[final,3p,times]{elsarticle}
%% \documentclass[final,3p,times,twocolumn]{elsarticle}
%% \documentclass[final,5p,times]{elsarticle}
%% \documentclass[final,5p,times,twocolumn]{elsarticle}

%% For including figures, graphicx.sty has been loaded in
%% elsarticle.cls. If you prefer to use the old commands
%% please give \usepackage{epsfig}

%% The amssymb package provides various useful mathematical symbols
\usepackage{amssymb}
%% The amsmath package provides various useful equation environments.
\usepackage{amsmath}
%% The amsthm package provides extended theorem environments
%% \usepackage{amsthm}

%% The lineno packages adds line numbers. Start line numbering with
%% \begin{linenumbers}, end it with \end{linenumbers}. Or switch it on
%% for the whole article with \linenumbers.
%% \usepackage{lineno}

\journal{Nuclear Physics B}

\begin{document}

\begin{frontmatter}

%% Title, authors and addresses

%% use the tnoteref command within \title for footnotes;
%% use the tnotetext command for theassociated footnote;
%% use the fnref command within \author or \affiliation for footnotes;
%% use the fntext command for theassociated footnote;
%% use the corref command within \author for corresponding author footnotes;
%% use the cortext command for theassociated footnote;
%% use the ead command for the email address,
%% and the form \ead[url] for the home page:
%% \title{Title\tnoteref{label1}}
%% \tnotetext[label1]{}
%% \author{Name\corref{cor1}\fnref{label2}}
%% \ead{email address}
%% \ead[url]{home page}
%% \fntext[label2]{}
%% \cortext[cor1]{}
%% \affiliation{organization={},
%%             addressline={},
%%             city={},
%%             postcode={},
%%             state={},
%%             country={}}
%% \fntext[label3]{}

\title{$\Phi$KFM: Physically-Constrained Sign Language Generation via Attention-LSTM and Differentiable Kalman Filtering}

%% use optional labels to link authors explicitly to addresses:
%% \author[label1,label2]{}
%% \affiliation[label1]{organization={},
%%             addressline={},
%%             city={},
%%             postcode={},
%%             state={},
%%             country={}}
%%
%% \affiliation[label2]{organization={},
%%             addressline={},
%%             city={},
%%             postcode={},
%%             state={},
%%             country={}}

\author[label1]{Qinkun Xiao} %% Author name
\author[label2]{Peiran Liu}
\author[label2]{Lu Li}
\author{Jielei Xiao}
%% Author affiliation
\affiliation[label1]{organization={Department of Electrical and Information Engineering, Xi'an Technological University},%Department and Organization
            addressline={No.2 Xuefu Middle Road}, 
            city={Xi'an},
            postcode={}, 
            state={Shaanxi},
            country={China}}

%% Author affiliation
\affiliation[label2]{organization={Department of Mechanical and Electrical Engineering, Xi'an Technological University},%Department and Organization
            addressline={No.2 Xuefu Middle Road}, 
            city={Xi'an},
            postcode={}, 
            state={Shaanxi},
            country={China}}
                

%% Abstract
\begin{abstract}
We present $\Phi$KFM, a novel sign language generation (SLG) framework that combines an attention-enhanced with differentiable Kalman filtering model (KFM) to address linguistic fidelity, biomechanical compliance, and spatiotemporal coherence. $\Phi$KFM  explicitly integrates physical constraints through parallel LSTM streams for kinematic synchronization and probabilistic filtering, eliminating the need for post-processing. Our composite loss function, balancing KL divergence and biomechanical penalties, achieves a joint compliance rate of 91.8\% (a 3.7\% improvement) and reduces acceleration discontinuities by 62.7\%. Experimental results demonstrate superior motion naturalness (MOS: 4.7/5.0) with only a 14ms computational overhead. Key innovations include: 1) physics-aware attention for motion-text alignment, 2) differentiable filtering to enforce anatomical constraints, and 3) adaptive curriculum learning. $\Phi$KFM advances assistive communication and establishes new standards for physically-constrained motion generation.
\end{abstract}

%%Graphical abstract
\begin{graphicalabstract}
%\includegraphics{grabs}
\end{graphicalabstract}

%%Research highlights
\begin{highlights}
\item Research highlight 1
\item Research highlight 2
\end{highlights}

%% Keywords
\begin{keyword}
SLG \sep KFM \sep biomechanical compliance \sep LSTM


\end{keyword}

\end{frontmatter}

%% Add \usepackage{lineno} before \begin{document} and uncomment 
%% following line to enable line numbers
%% \linenumbers

%% main text
%%

%% Use \section commands to start a section
\section{Introduction}
\label{sec1}
%% Labels are used to cross-reference an item using \ref command.
As the inverse process of sign language recognition (SLR), sign language generation (SLG) aims to translate linguistic content into semantically accurate and biomechanically plausible sign sequences \cite{ref1}. Despite substantial progress in SLR, where modern approaches achieve recognition accuracies exceeding 92\% in controlled environments \cite{ref2}, SLG still faces persistent challenges in linguistics, biomechanics, and computer science \cite{ref3}.

An effective SLG system must simultaneously address three interconnected challenges. First, linguistic fidelity must be preserved, since sign languages possess unique grammar and syntax that differ significantly from spoken languages \cite{ref4}. These complex spatial-kinematic systems involve the precise coordination of handshapes, movements, spatial locations, and facial expressions \cite{ref5}. Early rule-based systems employed finite state machines and kinematic interpolation \cite{ref6} but lacked flexibility to accommodate natural variability \cite{ref7}. Although modern deep learning techniques have improved semantic representation \cite{ref8}, maintaining grammatical accuracy while capturing nuanced natural variations remains a significant challenge \cite{ref9}.

Additionally, biomechanical compliance is essential due to strict physiological constraints often violated by current generative models \cite{ref10}. These constraints encompass joint angle ranges (e.g., finger joints typically 0°-90°) \cite{ref11}, velocity thresholds (e.g., elbow velocities below 1.5 m/s) \cite{ref12} , characteristic acceleration patterns (e.g., damped harmonic motion of the wrist) \cite{ref13}, and principles of energy minimization \cite{ref14}. According to the ASL-Physics 2023 benchmark, approximately 34\% of generated joint angles exceed biomechanical limits \cite{ref15}, resulting in unnatural or physically implausible movements \cite{ref16}, which significantly undermines communicative clarity \cite{ref17}.

Furthermore, achieving spatiotemporal coherence—which entails precise temporal coordination and smooth motion transitions—remains a major challenge \cite{ref18}. Current systems frequently produce discontinuous motion, with a discontinuity rate 58\% higher than that of human signers, as observed in the SignGen-2023 dataset \cite{ref19}. This issue stems from insufficient modeling of inter-articular dependencies \cite{ref20}, limited phrase-level contextual windows \cite{ref21}, and inconsistencies between training and inference conditions \cite{ref22}. These discontinuities hinder both the naturalness and intelligibility of the generated motion, as rhythmic features of signing convey essential linguistic cues \cite{ref23}.

SLG methodologies have undergone significant evolution over the past decades \cite{ref24}. Early rule-based approaches relied heavily on finite state machines, handcrafted databases, and kinematic interpolation techniques \cite{ref25}, offering precise control yet lacking flexibility \cite{ref26}. Statistical learning techniques (2015-2020), including Hidden Markov Models \cite{ref27}, Dynamic Time Warping \cite{ref28}, and Gaussian Mixture Models \cite{ref29}, improved generalizability but were inadequate in handling the high dimensionality and complexity of sign language data. Recent deep learning approaches include LSTM architectures effective in temporal modeling yet prone to error accumulation, Transformer-based models that handle long-range dependencies but incur higher computational complexity, and generative models like GANs, VAEs, and diffusion models, which often produce physically implausible motions.

Specialized techniques for enforcing biomechanical constraints—such as post-processing filters, Lagrangian neural networks, and physics-inspired loss functions—have shown promise in improving motion plausibility. However, they often introduce computational latency and demand extensive parameter tuning. Moreover, these methods typically regard physical constraints as auxiliary components, rather than integrating them into the core generation process.

In response, this paper introduces $\Phi$KFM, a novel SLG framework that fundamentally rethinks the integration of physical constraints into the generative architecture. $\Phi$KFM combines an attention-enhanced LSTM with a differentiable Kalman filtering model to simultaneously address linguistic fidelity, biomechanical compliance, and spatiotemporal coherence. The framework employs a dual-stream synchronization mechanism that jointly models joint coordinates and kinematic states, while probabilistic filtering ensures real-time enforcement of physiological constraints. Additionally, a composite loss function—blending KL divergence, biomechanical penalties, and coherence metrics—guides training under an adaptive curriculum, offering both efficiency and robustness.

Implementation advantages include exceptional computational efficiency with minimal overhead (14ms per frame) and robust generalization across CSL500, WLASL, and Kinetics datasets, demonstrating improvements in joint compliance (91.8\%, +3.7\% over baselines), reduced acceleration discontinuities (62.7\%), and temporal naturalness (mean opinion scores of 4.7/5.0). Qualitative assessments further confirm enhancements in elbow trajectory realism, finger coordination, and phrase-level rhythm.

Beyond technical contributions, $\Phi$KFM supports accessible communication technologies, improves SL avatars, and enhances educational applications. It also opens new research pathways in physics-informed generative models and real-time constrained optimization, advancing telepresence, digital interpretation, and VR/AR platforms. This work marks a significant paradigm shift by placing biomechanical constraints at the core of the generative process, demonstrating notable improvements in both objective metrics and subjective naturalness assessments. Future developments will explore extending $\Phi$KFM to full-body signing and interactive systems, further promoting accessible communication technologies.


%% Use \subsection commands to start a subsection.
\section{Methods}
\subsection{Overview}
\label{subsec1}
The overall framework of proposed method is illustrated in Fig 1. The main novelty of our approach is to combine deep neural network (DNN) sequence generation models and probabilistic graphical models (PGM) for generating SL obeyed real-world physical motion laws. The proposed model contains two main components: the DNN and the PGM, we call the fused model $\Phi$KFM. The DNN includes encoder, attention, and decoder modules. In DNN, the input is a text sequence $\mathbf{S}=(s_1,\cdots,s_n)$, and the decoder outputs an initial estimated SL skeleton sequence $\hat{\mathbf{K}}=({\hat{k}}_1,\cdots,{\hat{k}}_T)$. In PGM, the Kalman filtering model (KFM) is used for skeleton data updating, yielding an updated skeleton sequence $\mathbf{X}=(\mathbf{x}_1,\cdots,\mathbf{x}_T)$, that conforms to real-world physical motion laws. The loss function $\mathcal{L}$ is the distribution distance between the true skeleton sequence $\mathbf{K}^{real}$ corresponding to the text $\mathbf{S}$ and the optimal generated skeleton sequence $\mathbf{X}$. In system learning, we consider the loss function with physical constraints ($\mathbf{U}$) as the optimization objective.

\begin{figure}[t]%% placement specifier
\centering%% For centre alignment of image.
\includegraphics{Figure_1.pdf}
\caption{Figure Caption}\label{fig1}
\end{figure}

\subsection{Module Design}
\paragraph{Deep Network}
The first and basic component is encoder ($f_e$), as shown in Fig.1(a). The module encodes the text sequence $\mathbf{S}=(s_1,\cdots,s_T)$ into the feature sequence $\mathbf{h}=(h_1,\cdots,h_T)$. We use a BiLSTM network as the encoder, and our model can effectively represent the context information. When $\mathbf{S} $is fed into encoder $f_e$, we have:
\begin{equation}
{cv}_t=\sum_{i=1}^{T}{\alpha_{ti}h_i}
\end{equation}
%% Use \subsubsection, \paragraph, \subparagraph commands to 
%% start 3rd, 4th and 5th level sections.
%% Refer following link for more details.
%% https://en.wikibooks.org/wiki/LaTeX/Document_Structure#Sectioning_commands

\subsubsection{Mathematics}
%% Inline mathematics is tagged between $ symbols.
This is an example for the symbol $\alpha$ tagged as inline mathematics.

%% Displayed equations can be tagged using various environments. 
%% Single line equations can be tagged using the equation environment.
\begin{equation}
f(x) = (x+a)(x+b)
\end{equation}

%% Unnumbered equations are tagged using starred versions of the environment.
%% amsmath package needs to be loaded for the starred version of equation environment.
\begin{equation*}
f(x) = (x+a)(x+b)
\end{equation*}

%% align or eqnarray environments can be used for multi line equations.
%% & is used to mark alignment points in equations.
%% \\ is used to end a row in a multiline equation.
\begin{align}
 f(x) &= (x+a)(x+b) \\
      &= x^2 + (a+b)x + ab
\end{align}

\begin{eqnarray}
 f(x) &=& (x+a)(x+b) \nonumber\\ %% If equation numbering is not needed for a row use \nonumber.
      &=& x^2 + (a+b)x + ab
\end{eqnarray}

%% Unnumbered versions of align and eqnarray
\begin{align*}
 f(x) &= (x+a)(x+b) \\
      &= x^2 + (a+b)x + ab
\end{align*}

\begin{eqnarray*}
 f(x)&=& (x+a)(x+b) \\
     &=& x^2 + (a+b)x + ab
\end{eqnarray*}

%% Refer following link for more details.
%% https://en.wikibooks.org/wiki/LaTeX/Mathematics
%% https://en.wikibooks.org/wiki/LaTeX/Advanced_Mathematics

%% Use a table environment to create tables.
%% Refer following link for more details.
%% https://en.wikibooks.org/wiki/LaTeX/Tables
\begin{table}[t]%% placement specifier
%% Use tabular environment to tag the tabular data.
%% https://en.wikibooks.org/wiki/LaTeX/Tables#The_tabular_environment
\centering%% For centre alignment of tabular.
\begin{tabular}{l c r}%% Table column specifiers
%% Tabular cells are separated by &
  1 & 2 & 3 \\ %% A tabular row ends with \\
  4 & 5 & 6 \\
  7 & 8 & 9 \\
\end{tabular}
%% Use \caption command for table caption and label.
\caption{Table Caption}\label{tab1}
\end{table}


%% Use figure environment to create figures
%% Refer following link for more details.
%% https://en.wikibooks.org/wiki/LaTeX/Floats,_Figures_and_Captions
\begin{figure}[t]%% placement specifier
%% Use \includegraphics command to insert graphic files. Place graphics files in 
%% working directory.
\centering%% For centre alignment of image.
\includegraphics{example-image-a}
%% Use \caption command for figure caption and label.
\caption{Figure Caption}\label{fig1}
%% https://en.wikibooks.org/wiki/LaTeX/Importing_Graphics#Importing_external_graphics
\end{figure}


%% The Appendices part is started with the command \appendix;
%% appendix sections are then done as normal sections
\appendix
\section{Example Appendix Section}
\label{app1}

Appendix text.

%% For citations use: 
%%       \cite{<label>} ==> [1]

%%
Example citation, See \cite{yu2023signavatars}.

%% If you have bib database file and want bibtex to generate the
%% bibitems, please use
%%
%%  \bibliographystyle{elsarticle-num} 
%%  \bibliography{<your bibdatabase>}

%% else use the following coding to input the bibitems directly in the
%% TeX file.

%% Refer following link for more details about bibliography and citations.
%% https://en.wikibooks.org/wiki/LaTeX/Bibliography_Management

\begin{thebibliography}{99}

%% For numbered reference style
%% \bibitem{label}
%% Text of bibliographic item

\bibitem{ref1}
Yu, Z., Huang, S., Cheng, Y., \& Birdal, T.
\textit{SignAvatars: A Large-scale 3D Sign Language Holistic Motion Dataset and Benchmark}.
arXiv:2310.20436, 2023.

\bibitem{ref2}
Li, Y., Zhang, Y., Zhao, Z., et al.
\textit{CSL: A large-scale Chinese scientific literature dataset}.
arXiv:2209.05034, 2022.

\bibitem{ref3}
Deng, Z., Leng, Y., Chen, J., et al.
\textit{TMS-Net: A multi-feature multi-stream multi-level information sharing network for skeleton-based sign language recognition}.
Neurocomputing, 2024, 572: 127194.

\bibitem{ref4}
Koller, O., Zargaran, S., Ney, H., et al.
\textit{Deep Sign: Enabling Robust Statistical Continuous Sign Language Recognition via Hybrid CNN-HMMs}.
Int J Comput Vis, 2018, 126: 1311–1325.

\bibitem{ref5}
Xie, P., Peng, T., Du, Y., \& Zhang, Q.
\textit{Sign Language Production with Latent Motion Transformer}.
2024 IEEE/CVF Winter Conference on Applications of Computer Vision (WACV), Waikoloa, HI, USA, 2024, pp. 3012-3022.

\bibitem{ref6}
Renuka, D. K., Kumar, L. A., Harini, K. R., et al.
\textit{Sign Language Production Using Generative AI}.
International Conference on Computing and Intelligent Reality Technologies (ICCIRT), Coimbatore, India, 2024, pp. 33-38.

\bibitem{ref7}
Saunders, B., Camgöz, N. C., \& Bowden, R.
\textit{Progressive Transformers for End-to-End Sign Language Production}.
ECCV, 2020, pp. 687-705.

\bibitem{ref8}
Arib, S. H., Akter, R., Rahman, S., \& Rahman, S.
\textit{SignFormer-GCN: Continuous sign language translation using spatio-temporal graph convolutional networks}.
PLoS One, 2025, 20(2): e0316298.

\bibitem{ref9}
Chaudhary, L., Ananthanarayana, T., Hoq, E., et al.
\textit{Signnet ii: A transformer-based two-way sign language translation model}.
IEEE Transactions on Pattern Analysis and Machine Intelligence, 2022, 45(11): 12896-12907.

\bibitem{ref10}
Zhou, B., Chen, Z., Clapés, A., et al.
\textit{Gloss-Free Sign Language Translation: Improving from Visual-Language Pretraining}.
arXiv:2307.14768, 2023.

\bibitem{ref11}
Lin, K., Wang, X., Zhu, L., et al.
\textit{Gloss-Free End-to-End Sign Language Translation}.
arXiv:2305.12876, 2023.

\bibitem{ref12}
Yang, X., Lim, Z., Jung, H., et al.
\textit{Estimation of Finite Finger Joint Centers of Rotation Using 3D Hand Skeleton Motions Reconstructed from CT Scans}.
Appl. Sci., 2020, 10: 9129.

\bibitem{ref13}
Qazi, A., et al.
\textit{ExerAIde: AI-assisted Multimodal Diagnosis for Enhanced Sports Performance and Personalised Rehabilitation}.
In CVPR Workshops, 2024.

\bibitem{ref14}
Sartinas, E. G., Psarakis, E. Z., \& Kosmopoulos, D. I.
\textit{Motion-based sign language video summarization using curvature and torsion}.
arXiv:2305.16801, 2023.

\bibitem{ref15}
Zhang, H., Goodfellow, I., Metaxas, D., et al.
\textit{StyleSwin: Transformer-Based GAN for High-Resolution Image Generation}.
In CVPR, 2022.

\bibitem{ref16}
Chen, L., Xu, Y., Zhu, Q.-X., \& He, Y.-L.
\textit{Adaptive Multi-Head Self-Attention Based Supervised VAE for Industrial Soft Sensing With Missing Data}.
IEEE Transactions on Automation Science and Engineering, 2024, 21(3): 3564-3575.

\bibitem{ref17}
Yuan, W., et al.
\textit{MoGenTS: Motion Generation based on Spatial-Temporal Joint Modeling}.
In NeurIPS, 2024.

\bibitem{ref18}
Shlezinger, N., et al.
\textit{AI-Aided Kalman Filters}.
arXiv:2410.12289, 2025.

\bibitem{ref19}
Cranmer, M., Greydanus, S., Hoyer, S., et al.
\textit{Lagrangian neural networks}.
arXiv:2003.04630, 2020.

\bibitem{ref20}
Jin, X.-B., Chen, W., Ma, H.-J., et al.
\textit{Parameter-Free State Estimation Based on Kalman Filter with Attention Learning for GPS Tracking in Autonomous Driving System}.
Sensors, 2023, 23(20): 8650.

\bibitem{ref21}
Villa-Monedero, M., Gil-Martín, M., Sáez-Trigueros, D., et al.
\textit{Sign Language Dataset for Automatic Motion Generation}.
Journal of Imaging, 2023, 9(12): 262.

\bibitem{ref22}
Feng, S., Li, X., Zhang, S., et al.
\textit{A review: state estimation based on hybrid models of Kalman filter and neural network}.
Systems Science \& Control Engineering, 2023, 11(1): 2173682.

\bibitem{ref23}
Zhang, H., Goodfellow, I., Metaxas, D., \& Odena, A.
\textit{StyleSwin: Transformer-Based GAN for High-Resolution Image Generation}.
In CVPR, 2022.

\bibitem{ref24}
Natarajan, B., \& Elakkiya, R.
\textit{Dynamic GAN for High-Quality Sign Language Video Generation from Skeletal Poses Using Generative Adversarial Networks}.
Soft Computing, 2021, 26(24): 12947–12960.

\bibitem{ref25}
Wang, Y., \& Zhang, H.
\textit{Updated Prediction of Air Quality Based on Kalman-Attention-LSTM Model}.
Sustainability, 2023, 15(1): 356.

\bibitem{ref26}
Yu, Z., Huang, S., Cheng, Y., \& Birdal, T.
\textit{SignAvatars: A Large-scale 3D Sign Language Holistic Motion Dataset and Benchmark}.
arXiv:2310.20436, 2023.

\bibitem{ref27}
Dong, L., Wang, X., \& Nwogu, I.
\textit{Word-Conditioned 3D American Sign Language Motion Generation}.
In Findings of the Association for Computational Linguistics: EMNLP 2024, pp. 9993–9999.

\bibitem{ref28}
Ranum, O., Otterspeer, G., Andersen, J. I., Belleman, R. G., \& Roelofsen, F.
\textit{3D-LEX v1.0: 3D Lexicons for American Sign Language and Sign Language of the Netherlands}.
arXiv:2409.01901, 2024.

\bibitem{ref29}
Saunders, B., Camgoz, N. C., \& Bowden, R.
\textit{Everybody Sign Now: Translating Spoken Language to Photo Realistic Sign Language Video}.
arXiv:2011.09846, 2020.

\end{thebibliography}
\end{document}

\endinput
%%
%% End of file `elsarticle-template-num.tex'.
